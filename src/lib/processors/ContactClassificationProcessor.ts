import { BaseProcessor, EntityData, ProcessorOptions } from './BaseProcessor'
import { ContactProcessingState } from '../../types/processing'
import { 
  CONTACT_CLASSIFICATION_SYSTEM_PROMPT,
  CONTACT_CLASSIFICATION_USER_TEMPLATE_FUNCTION 
} from '../prompts'
import { LLMFactory, LLMMessage, createProcessorLoggerAdapter, LogLevel } from '../llm'

interface ContactData extends EntityData {
  contact_id: number
  email: string
  first_name?: string
  last_name?: string
  title?: string
  linkedin_url?: string
  company_id?: number
  company_name?: string
  company_website?: string
  industry?: string
  contact_country?: string
  processing_state: ContactProcessingState
  extracted?: boolean
  contact_category?: string
  capital_type?: string
  osint_research?: Record<string, unknown>
  overview_data?: Record<string, unknown>
}


interface ClassificationResult {
  category: 'Capital Source' | 'Sponsor' | 'Other'
  confidence: number
  reasoning: string
}

export class ContactClassificationProcessor extends BaseProcessor {
  private llmProvider;

  constructor(options: ProcessorOptions = {}) {
    super('ContactClassification', options)
    
    // Create a logger adapter that uses our log method
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));
    
    // Create LLM provider with Perplexity as primary and OpenAI as fallback
    this.llmProvider = LLMFactory.createWithFallback(
      'perplexity',
      'openai',
      loggerAdapter,
      process.env.PERPLEXITY_API_KEY,
      process.env.OPENAI_API_KEY,
      { 
        temperature: 0.2,
        maxTokens: 8000
      }
    );
  }

  async getUnprocessedEntities(): Promise<EntityData[]> {
    const contacts = await this.getContactsForClassification(
      this.options.limit,
      this.options.singleId
    )
    
    return contacts.map(contact => ({
      id: contact.contact_id as number,
      ...contact
    }))
  }

  async processEntity(entity: EntityData): Promise<{ success: boolean; error?: string }> {
    const contact = entity as ContactData
    
    try {
      this.log('info', `Classifying contact ${contact.contact_id}: ${contact.first_name} ${contact.last_name}`)
      
      // Get OSINT research
      const osintResearch = await this.getContactOSINTResearch(contact.contact_id)
      contact.osint_research = osintResearch || {}  
      
      const overviewData = await this.getContactOverviewData(contact.contact_id)
      contact.overview_data = overviewData || {}
      
      // Set status to running
      await this.updateContactClassificationStatus(contact.contact_id, 'running')
      
      // Perform contact classification
      const classificationResult = await this.classifyContact(contact)
      
      if (classificationResult.success) {
        this.log('info', `Contact classification completed for ${contact.first_name} ${contact.last_name}: ${classificationResult.category}`)
        return { success: true }
      } else {
        return { success: false, error: classificationResult.error }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error classifying contact ${contact.contact_id}: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    if (success) {
      // Classification completed successfully
      await this.updateContactClassificationStatus(entityId, 'completed')
    } else {
      // Classification failed
      await this.updateContactClassificationStatus(entityId, 'failed', error)
      await this.incrementProcessingErrorCount('contact', entityId)
    }
  }

  /**
   * Extract detailed profile data and classify contact
   */
  private async classifyContact(contact: ContactData): Promise<{ 
    success: boolean; 
    category?: string; 
    capitalType?: string; 
    error?: string 
  }> {
    try {
      this.log('info', `Starting profile extraction and classification for contact ${contact.contact_id}`)
      
      // Build the extraction prompt
      const prompt = await this.buildExtractionPrompt(contact)
      
      // Call LLM API for detailed profile extraction
      this.log('debug', `Calling LLM API for profile extraction for contact ${contact.contact_id}`)
      const apiResponse = await this.callLLMAPI(prompt)
      
      if (!apiResponse.success) {
        return { success: false, error: apiResponse.error }
      }
      
      // Parse the response to extract profile data
      const extractedData = this.parseExtractionResponse(apiResponse.content!)
      if (!extractedData) {
        return { success: false, error: 'Failed to parse extraction response' }
      }
      
      // Save the extracted data to database
      const saveResult = await this.saveExtractedData(contact.contact_id, extractedData, apiResponse.tokens!)
      
      if (!saveResult.success) {
        return { success: false, error: saveResult.error }
      }
      
      // Determine category based on extracted data
      const category = this.determineCategoryFromData(extractedData, contact)
      const capitalType = this.determineCapitalTypeFromData(extractedData, contact)
      
      // Save classification results to contacts table
      const classificationUpdateResult = await this.updateContactWithClassificationResults(contact.contact_id, category, capitalType)
      
      if (!classificationUpdateResult.success) {
        return { success: false, error: classificationUpdateResult.error }
      }
      
      this.log('info', `Profile extraction and classification completed for contact ${contact.contact_id}: ${category} - ${capitalType}`)
      
      return { 
        success: true, 
        category, 
        capitalType 
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Build extraction prompt for detailed profile data
   */
  private async buildExtractionPrompt(contact: ContactData): Promise<{ system: string; user: string }> {
    const userPrompt = CONTACT_CLASSIFICATION_USER_TEMPLATE_FUNCTION({
      first_name: contact.first_name || '',
      last_name: contact.last_name || '',
      email: contact.email,
      title: contact.title,
      company_name: contact.company_name,
      company_website: contact.company_website,
      linkedin_url: contact.linkedin_url,
      industry: contact.industry,
      contact_country: contact.contact_country
    })

    return { 
      system: CONTACT_CLASSIFICATION_SYSTEM_PROMPT, 
      user: userPrompt 
    }
  }

  /**
   * Call LLM for profile extraction using our provider system
   */
  private async callLLMAPI(
    prompt: { system: string; user: string }
  ): Promise<{ success: boolean; content?: string; tokens?: number; error?: string }> {
    try {
      const messages: LLMMessage[] = [
        { role: 'system', content: prompt.system },
        { role: 'user', content: prompt.user }
      ];

      // Use the LLM provider with fallback support
      const response = await this.llmProvider.callLLM(messages, {
        temperature: 0.2,
        maxTokens: 8000
      });

      if (!response.content) {
        return { success: false, error: `No content in API response from ${response.provider}` };
      }

      const tokens = response.usage?.totalTokens || 0;
      this.log('info', `API call successful using ${response.provider} (${response.model}). Tokens used: ${tokens}`);
      
      return { 
        success: true, 
        content: response.content, 
        tokens: tokens 
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.log('error', `LLM API call failed: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Parse extraction response to extract structured data
   */
  private parseExtractionResponse(content: string): any | null {
    try {
      this.log('debug', `Parsing LLM response content (length: ${content.length})`)
      
      // First, try to extract JSON from markdown code blocks
      let jsonContent = content
      
      // Remove thinking tags if present
      jsonContent = jsonContent.replace(/<think>[\s\S]*?<\/think>/g, '')
      
      // Try to extract from markdown code blocks first
      const markdownJsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/)
      if (markdownJsonMatch) {
        jsonContent = markdownJsonMatch[1].trim()
        this.log('debug', `Extracted JSON from markdown code block`)
      } else {
        // Fallback: try to find JSON object in the content
        const jsonMatch = jsonContent.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          jsonContent = jsonMatch[0]
          this.log('debug', `Extracted JSON using fallback regex`)
        } else {
          this.log('warn', `No JSON found in response content`)
          return null
        }
      }
      
      // Clean up the JSON content
      jsonContent = jsonContent.trim()
      
      this.log('debug', `Attempting to parse JSON: ${jsonContent.substring(0, 200)}...`)
      
      const parsed = JSON.parse(jsonContent)
      
      // Check if this is a classification response (lead_type, confidence, rationale)
      if (parsed.lead_type && parsed.confidence !== undefined && parsed.rationale) {
        this.log('info', `Successfully parsed classification response: ${parsed.lead_type} (confidence: ${parsed.confidence})`)
        
        // Convert to the expected format for contact overview data
        return {
          executive_summary: `Classification: ${parsed.lead_type} (${parsed.confidence * 100}% confidence). ${parsed.rationale}`,
          career_timeline: [],
          notable_activities: [],
          personal_tidbits: [],
          conversation_hooks: [],
          outreach_draft: '',
          sources: [],
          classification: {
            category: parsed.lead_type === 'Capital Source' ? 'Capital Source' : 
                     parsed.lead_type === 'Sponsor' ? 'Sponsor' : 'Other',
            confidence: parsed.confidence,
            rationale: parsed.rationale
          }
        }
      }
      
      // Check if this is a full profile extraction response
      if (parsed.executive_summary || parsed.career_timeline || parsed.conversation_hooks) {
        this.log('info', `Successfully parsed full profile extraction response with ${Object.keys(parsed).length} fields`)
        return parsed
      }
      
      this.log('warn', `Parsed JSON but missing expected fields`)
      return null

    } catch (error) {
      this.log('error', `Failed to parse JSON response: ${error}`)
      
      // Log a portion of the content for debugging
      const contentPreview = content.length > 1500 ? content.substring(0, 1500) + '...' : content
      this.log('debug', `Response content preview: ${contentPreview}`)
      
      return null
    }
  }

  /**
   * Save extracted data to contact_extracted_data table
   */
  private async saveExtractedData(
    contactId: number, 
    extractedData: any,
    tokens: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const upsertSql = `
        INSERT INTO contact_extracted_data (
          contact_id, executive_summary, career_timeline, notable_activities,
          personal_tidbits, conversation_hooks, outreach_draft, sources, created_at, updated_at
        )
        VALUES ($1, $2, $3::jsonb, $4::jsonb, $5::jsonb, $6::jsonb, $7::jsonb, $8::jsonb, NOW(), NOW())
        ON CONFLICT (contact_id) DO UPDATE SET
          executive_summary = EXCLUDED.executive_summary,
          career_timeline = EXCLUDED.career_timeline,
          notable_activities = EXCLUDED.notable_activities,
          personal_tidbits = EXCLUDED.personal_tidbits,
          conversation_hooks = EXCLUDED.conversation_hooks,
          outreach_draft = EXCLUDED.outreach_draft,
          sources = EXCLUDED.sources,
          updated_at = NOW()
      `

      await this.query(upsertSql, [
        contactId,
        extractedData.executive_summary || '',
        JSON.stringify(extractedData.career_timeline || []),
        JSON.stringify(extractedData.notable_activities || []),
        JSON.stringify(extractedData.personal_tidbits || []),
        JSON.stringify(extractedData.conversation_hooks || []),
        JSON.stringify(extractedData.outreach_draft || ''),
        JSON.stringify(extractedData.sources || [])
      ])

      this.log('debug', `Saved extracted data to contact_extracted_data for contact ${contactId}`)
      return { success: true }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Failed to save extracted data: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Determine contact category based on extracted data
   */
  private determineCategoryFromData(extractedData: any, contact: ContactData): string {
    // Check if we have classification data from LLM
    if (extractedData.classification && extractedData.classification.category) {
      this.log('info', `Using LLM classification: ${extractedData.classification.category}`)
      
      // Map LLM response to database enum values
      const category = extractedData.classification.category
      if (category === 'Capital Source') {
        return 'investor'
      } else if (category === 'Sponsor') {
        return 'sponsor'
      } else {
        // For "Other" or any unknown values, fall back to heuristics
        return this.mapToDatabaseCategory(this.determineCategory(contact))
      }
    }
    
    const summary = (extractedData.executive_summary || '').toLowerCase()
    const timeline = JSON.stringify(extractedData.career_timeline || []).toLowerCase()
    const activities = JSON.stringify(extractedData.notable_activities || []).toLowerCase()
    
    const allText = `${summary} ${timeline} ${activities}`.toLowerCase()
    
    // Enhanced classification based on extracted data
    const capitalSourceKeywords = [
      'investor', 'fund manager', 'capital', 'equity', 'lender', 'financing',
      'investment', 'venture', 'private equity', 'hedge fund', 'financing',
      'provides funding', 'invests in', 'capital allocation'
    ]
    
    const sponsorKeywords = [
      'developer', 'sponsor', 'acquisition', 'development', 'project manager',
      'real estate development', 'property development', 'seeks funding',
      'raising capital', 'fundraising'
    ]
    
    const capitalSourceScore = capitalSourceKeywords.reduce((score, keyword) => 
      allText.includes(keyword) ? score + 1 : score, 0
    )
    
    const sponsorScore = sponsorKeywords.reduce((score, keyword) => 
      allText.includes(keyword) ? score + 1 : score, 0
    )
    
    // Fall back to basic heuristics if extracted data doesn't provide clear indicators
    if (capitalSourceScore === 0 && sponsorScore === 0) {
      return this.determineCategory(contact)
    }
    
    return capitalSourceScore > sponsorScore ? 'investor' : 'sponsor'
  }

  /**
   * Map internal category names to database enum values
   */
  private mapToDatabaseCategory(internalCategory: string): string {
    if (internalCategory === 'Capital Source') {
      return 'investor'
    } else if (internalCategory === 'Sponsor') {
      return 'sponsor'
    } else {
      // Default to investor for unknown categories
      return 'investor'
    }
  }

  /**
   * Determine capital type based on extracted data
   */
  private determineCapitalTypeFromData(extractedData: any, contact: ContactData): string {
    // Check if we have classification rationale that mentions capital type
    if (extractedData.classification && extractedData.classification.rationale) {
      const rationale = extractedData.classification.rationale.toLowerCase()
      
      if (rationale.includes('debt') || rationale.includes('lending') || rationale.includes('loan')) {
        return 'Debt Capital'
      } else if (rationale.includes('venture') || rationale.includes('vc')) {
        return 'Venture Capital'
      } else if (rationale.includes('real estate') || rationale.includes('property')) {
        return 'Real Estate Investment'
      } else if (rationale.includes('private equity') || rationale.includes('pe')) {
        return 'Private Equity'
      }
    }
    
    const summary = (extractedData.executive_summary || '').toLowerCase()
    const timeline = JSON.stringify(extractedData.career_timeline || []).toLowerCase()
    const activities = JSON.stringify(extractedData.notable_activities || []).toLowerCase()
    
    const allText = `${summary} ${timeline} ${activities}`.toLowerCase()
    
    if (allText.includes('debt') || allText.includes('lending') || allText.includes('loan')) {
      return 'Debt Capital'
    } else if (allText.includes('venture') || allText.includes('vc')) {
      return 'Venture Capital'
    } else if (allText.includes('real estate') || allText.includes('property')) {
      return 'Real Estate Investment'
    } else if (allText.includes('private equity') || allText.includes('pe')) {
      return 'Private Equity'
    } else {
      // Fall back to basic heuristics
      return this.determineCapitalType(contact)
    }
  }

  /**
   * Build classification prompt for AI
   */
  private buildClassificationPrompt(contact: ContactData): string {
    let prompt = `Classify this contact in the real estate investment industry:\n\n`
    prompt += `Name: ${contact.first_name} ${contact.last_name}\n`
    
    if (contact.title) {
      prompt += `Title: ${contact.title}\n`
    }
    
    if (contact.company_name) {
      prompt += `Company: ${contact.company_name}\n`
    }
    
    prompt += `\nDetermine if this person is:\n`
    prompt += `1. Capital Source (provides funding/investment)\n`
    prompt += `2. Sponsor (seeks funding for deals)\n\n`
    prompt += `Also identify the type of capital they work with:\n`
    prompt += `- Private Equity\n`
    prompt += `- Debt Capital\n`
    prompt += `- Venture Capital\n`
    prompt += `- Real Estate Investment\n`
    prompt += `- Other\n`
    
    return prompt
  }

  /**
   * Determine contact category using simple heuristics
   */
  private determineCategory(contact: ContactData): string {
    const title = (contact.title || '').toLowerCase()
    const company = (contact.company_name || '').toLowerCase()
    
    // Capital Source indicators
    const capitalSourceKeywords = [
      'investor', 'fund', 'capital', 'equity', 'lender', 'financing',
      'investment', 'venture', 'private equity', 'hedge fund'
    ]
    
    // Sponsor indicators
    const sponsorKeywords = [
      'developer', 'sponsor', 'acquisition', 'development', 'project',
      'real estate', 'property', 'asset management'
    ]
    
    const titleAndCompany = `${title} ${company}`
    
    const capitalSourceScore = capitalSourceKeywords.reduce((score, keyword) => 
      titleAndCompany.includes(keyword) ? score + 1 : score, 0
    )
    
    const sponsorScore = sponsorKeywords.reduce((score, keyword) => 
      titleAndCompany.includes(keyword) ? score + 1 : score, 0
    )
    
    if (capitalSourceScore > sponsorScore) {
      return 'investor'
    } else if (sponsorScore > capitalSourceScore) {
      return 'sponsor'
    } else {
      // Default to investor if unclear
      return 'investor'
    }
  }

  /**
   * Determine capital type using simple heuristics
   */
  private determineCapitalType(contact: ContactData): string {
    const title = (contact.title || '').toLowerCase()
    const company = (contact.company_name || '').toLowerCase()
    const titleAndCompany = `${title} ${company}`
    
    if (titleAndCompany.includes('debt') || titleAndCompany.includes('lending') || titleAndCompany.includes('loan')) {
      return 'Debt Capital'
    } else if (titleAndCompany.includes('venture') || titleAndCompany.includes('vc')) {
      return 'Venture Capital'
    } else if (titleAndCompany.includes('real estate') || titleAndCompany.includes('property')) {
      return 'Real Estate Investment'
    } else if (titleAndCompany.includes('private equity') || titleAndCompany.includes('pe')) {
      return 'Private Equity'
    } else {
      return 'Private Equity' // Default
    }
  }

  /**
   * Update contact with classification results
   */
  private async updateContactWithClassificationResults(
    contactId: number, 
    category?: string, 
    capitalType?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const sql = `
        UPDATE contacts 
        SET category = $1, 
            capital_type = $2, 
            extracted = true,
            updated_at = NOW() 
        WHERE contact_id = $3
      `
      
      await this.query(sql, [category, capitalType, contactId])
      
      this.log('debug', `Updated contact ${contactId} with classification: ${category}, capital type: ${capitalType}`)
      return { success: true }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Failed to update contact classification: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Get classification statistics
   */
  async getClassificationStats(): Promise<{
    total: number
    classified: number
    capital_sources: number
    sponsors: number
    pending: number
  }> {
    const sql = `
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN extracted = true THEN 1 END) as classified,
        COUNT(CASE WHEN category = 'Capital Source' THEN 1 END) as capital_sources,
        COUNT(CASE WHEN category = 'Sponsor' THEN 1 END) as sponsors,
        COUNT(CASE WHEN extracted IS NULL OR extracted = false THEN 1 END) as pending
      FROM contacts
      WHERE searched = true
    `
    
    const result = await this.query(sql)
    const data = result[0] || {}
    
    return {
      total: parseInt(data.total as string) || 0,
      classified: parseInt(data.classified as string) || 0,
      capital_sources: parseInt(data.capital_sources as string) || 0,
      sponsors: parseInt(data.sponsors as string) || 0,
      pending: parseInt(data.pending as string) || 0
    }
  }
}
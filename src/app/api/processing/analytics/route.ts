import { NextRequest, NextResponse } from 'next/server';
import { pool } from '../../../../lib/db';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const timePeriod = searchParams.get('period') || 'daily';
  const stage = searchParams.get('stage') || 'all';
  const specificDate = searchParams.get('date');
  const specificMonth = searchParams.get('month');
  const specificYear = searchParams.get('year');

  try {
    const client = await pool.connect();
    
    // IMPORTANT: This API uses the correct business logic for stage eligibility:
    // - Email Verification: Only contacts with email addresses
    // - OSINT Research: Only contacts with completed email verification  
    // - Overview Extraction: Only contacts with completed OSINT research
    // - Classification: Only contacts with completed overview extraction
    // - Email Generation: Only contacts with completed classification
    // - Email Sending: Only contacts with completed email generation

        // Get processing volume trends
    const getTimeSeriesQuery = () => {
      let periods = timePeriod === 'daily' ? 7 : timePeriod === 'weekly' ? 4 : 12;
      const intervalUnit = timePeriod === 'daily' ? 'day' : timePeriod === 'weekly' ? 'week' : 'month';
      const truncUnit = timePeriod === 'daily' ? 'day' : timePeriod === 'weekly' ? 'week' : 'month';
      
      // Adjust date range based on specific date/month/year filters
      let dateFilter = '';
      if (specificDate) {
        dateFilter = `AND created_at::date = '${specificDate}'`;
        periods = 1; // Only show that specific day
      } else if (specificMonth) {
        dateFilter = `AND DATE_TRUNC('month', created_at) = '${specificMonth}-01'::date`;
        periods = 31; // Show all days in that month
      } else if (specificYear) {
        dateFilter = `AND DATE_TRUNC('year', created_at) = '${specificYear}-01-01'::date`;
        periods = 12; // Show all months in that year
      }

      return `
        WITH date_series AS (
          SELECT generate_series(
            CURRENT_DATE - INTERVAL '${periods - 1} ${intervalUnit}',
            CURRENT_DATE,
            INTERVAL '1 ${intervalUnit}'
          )::date AS date
        ),
        processing_data AS (
          SELECT 
            DATE_TRUNC('${truncUnit}', created_at) AS period_date,
            -- Count contacts that have completed any stage
            COUNT(CASE WHEN email_verification_status = 'completed' OR osint_status = 'completed' OR 
                              overview_extraction_status = 'completed' OR classification_status = 'completed' OR
                              email_generation_status = 'completed' OR email_sending_status = 'completed' 
                       THEN 1 END) as completed,
            -- Count contacts that have failed any stage
            COUNT(CASE WHEN email_verification_status = 'failed' OR osint_status = 'failed' OR 
                              overview_extraction_status = 'failed' OR classification_status = 'failed' OR
                              email_generation_status = 'failed' OR email_sending_status = 'failed' 
                       THEN 1 END) as failed,
            -- Count contacts that are pending (following correct stage eligibility)
            COUNT(CASE WHEN (email IS NOT NULL AND email != '' AND email != ' ' AND 
                            COALESCE(email_verification_status, 'pending') = 'pending') OR
                           (email_verification_status = 'completed' AND 
                            COALESCE(osint_status, 'pending') = 'pending') OR
                           (osint_status = 'completed' AND 
                            COALESCE(overview_extraction_status, 'pending') = 'pending') OR
                           (overview_extraction_status = 'completed' AND 
                            COALESCE(classification_status, 'pending') = 'pending') OR
                           (classification_status = 'completed' AND 
                            COALESCE(email_generation_status, 'pending') = 'pending') OR
                           (email_generation_status = 'completed' AND 
                            COALESCE(email_sending_status, 'pending') = 'pending')
                       THEN 1 END) as pending
          FROM contacts 
          WHERE created_at >= CURRENT_DATE - INTERVAL '${periods} ${intervalUnit}' ${dateFilter}
          GROUP BY period_date
        )
        SELECT 
          ds.date,
          COALESCE(pd.completed, 0) as completed,
          COALESCE(pd.failed, 0) as failed,
          COALESCE(pd.pending, 0) as pending
        FROM date_series ds
        LEFT JOIN processing_data pd ON ds.date = pd.period_date::date
        ORDER BY ds.date;
      `;
    };

    // Get stage performance data - using correct eligibility logic from stats API
    const stagePerformanceQuery = `
      SELECT 
        'email_verification' as stage,
        COUNT(CASE WHEN email_verification_status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN email_verification_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN email IS NOT NULL AND email != '' AND email != ' ' AND COALESCE(email_verification_status, 'pending') = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN email_verification_status = 'running' THEN 1 END) as running
      FROM contacts
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND email IS NOT NULL AND email != '' AND email != ' '
      
      UNION ALL
      
      SELECT 
        'osint' as stage,
        COUNT(CASE WHEN osint_status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN osint_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN email_verification_status = 'completed' AND COALESCE(osint_status, 'pending') = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN osint_status = 'running' THEN 1 END) as running
      FROM contacts
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND email_verification_status = 'completed'
      
      UNION ALL
      
      SELECT 
        'overview_extraction' as stage,
        COUNT(CASE WHEN overview_extraction_status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN overview_extraction_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN osint_status = 'completed' AND COALESCE(overview_extraction_status, 'pending') = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN overview_extraction_status = 'running' THEN 1 END) as running
      FROM contacts
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND osint_status = 'completed'
      
      UNION ALL
      
      SELECT 
        'classification' as stage,
        COUNT(CASE WHEN classification_status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN classification_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN overview_extraction_status = 'completed' AND COALESCE(classification_status, 'pending') = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN classification_status = 'running' THEN 1 END) as running
      FROM contacts
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND overview_extraction_status = 'completed'
      
      UNION ALL
      
      SELECT 
        'email_generation' as stage,
        COUNT(CASE WHEN email_generation_status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN email_generation_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN classification_status = 'completed' AND COALESCE(email_generation_status, 'pending') = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN email_generation_status = 'running' THEN 1 END) as running
      FROM contacts
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND classification_status = 'completed'
      
      UNION ALL
      
      SELECT 
        'email_sending' as stage,
        COUNT(CASE WHEN email_sending_status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN email_sending_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN email_generation_status = 'completed' AND COALESCE(email_sending_status, 'pending') = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN email_sending_status = 'running' THEN 1 END) as running
      FROM contacts
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND email_generation_status = 'completed'
      
      ORDER BY stage;
    `;

    // Get error analysis - using correct eligibility logic
    const errorAnalysisQuery = `
      WITH error_totals AS (
        SELECT 
          error_type,
          COUNT(*) as count
        FROM (
          SELECT 
            CASE 
              WHEN email_verification_error ILIKE '%invalid%' OR email_verification_error ILIKE '%not found%' THEN 'Invalid Email'
              WHEN osint_error ILIKE '%linkedin%' OR osint_error ILIKE '%not found%' THEN 'LinkedIn Not Found'
              WHEN email_verification_error ILIKE '%timeout%' OR osint_error ILIKE '%timeout%' OR 
                   overview_extraction_error ILIKE '%timeout%' OR classification_error ILIKE '%timeout%' OR
                   email_generation_error ILIKE '%timeout%' OR email_sending_error ILIKE '%timeout%' THEN 'API Timeout'
              WHEN email_verification_error ILIKE '%rate%' OR osint_error ILIKE '%rate%' OR 
                   overview_extraction_error ILIKE '%rate%' OR classification_error ILIKE '%rate%' OR
                   email_generation_error ILIKE '%rate%' OR email_sending_error ILIKE '%rate%' THEN 'Rate Limited'
              ELSE 'Other'
            END as error_type
          FROM contacts 
          WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
            AND (
              -- Email verification errors (eligible: contacts with emails)
              (email IS NOT NULL AND email != '' AND email != ' ' AND email_verification_status = 'failed') OR
              -- OSINT errors (eligible: contacts with verified emails)
              (email_verification_status = 'completed' AND osint_status = 'failed') OR
              -- Overview extraction errors (eligible: contacts with completed OSINT)
              (osint_status = 'completed' AND overview_extraction_status = 'failed') OR
              -- Classification errors (eligible: contacts with completed overview extraction)
              (overview_extraction_status = 'completed' AND classification_status = 'failed') OR
              -- Email generation errors (eligible: contacts with completed classification)
              (classification_status = 'completed' AND email_generation_status = 'failed') OR
              -- Email sending errors (eligible: contacts with completed email generation)
              (email_generation_status = 'completed' AND email_sending_status = 'failed')
            )
        ) error_summary
        GROUP BY error_type
      )
      SELECT 
        error_type,
        count,
        ROUND(count * 100.0 / SUM(count) OVER (), 1) as percentage
      FROM error_totals
      ORDER BY count DESC
      LIMIT 10;
    `;

    // Get processing speed metrics (mock data for now)
    const processingSpeedQuery = `
      SELECT 
        'email_verification' as stage,
        '2.3s' as avg_time
      UNION ALL
      SELECT 'osint', '45.2s'
      UNION ALL  
      SELECT 'overview_extraction', '12.8s'
      UNION ALL
      SELECT 'classification', '8.1s'
      UNION ALL
      SELECT 'email_generation', '15.6s'
      UNION ALL
      SELECT 'email_sending', '3.2s';
    `;

    // Get throughput metrics
    const throughputQuery = `
      SELECT 
        COUNT(*) as contacts_today,
        ROUND(COUNT(*) / 24.0, 0) as avg_per_hour,
        MAX(hourly_count) as peak_hour,
        ROUND(AVG(hourly_count), 0) as avg_hourly
      FROM (
        SELECT 
          DATE_TRUNC('hour', created_at) as hour,
          COUNT(*) as hourly_count
        FROM contacts 
        WHERE created_at >= CURRENT_DATE
        GROUP BY DATE_TRUNC('hour', created_at)
      ) hourly_stats;
    `;

    // Execute all queries
    const [timeSeriesResult, stagePerformanceResult, errorAnalysisResult, processingSpeedResult, throughputResult] = await Promise.all([
      client.query(getTimeSeriesQuery()),
      client.query(stagePerformanceQuery),
      client.query(errorAnalysisQuery),
      client.query(processingSpeedQuery),
      client.query(throughputQuery)
    ]);

    client.release();

    return NextResponse.json({
      success: true,
      data: {
        timeSeriesData: timeSeriesResult.rows,
        stagePerformance: stagePerformanceResult.rows,
        errorAnalysis: errorAnalysisResult.rows,
        processingSpeed: processingSpeedResult.rows,
        throughput: throughputResult.rows[0] || {
          contacts_today: 0,
          avg_per_hour: 0,
          peak_hour: 0,
          avg_hourly: 0
        },
        metadata: {
          timePeriod,
          stage,
          lastUpdated: new Date().toISOString(),
          dataSource: 'contacts table'
        }
      }
    });

  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch analytics data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 
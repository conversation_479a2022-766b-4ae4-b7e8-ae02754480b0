import { NextRequest, NextResponse } from 'next/server';
import { pool } from '../../../../lib/db';

interface AnalyticsConfig {
  enableRealTimeMetrics: boolean;
  enablePredictiveAnalytics: boolean;
  enableBottleneckDetection: boolean;
  cacheTimeout: number;
}

interface ProcessingSpeedMetric {
  stage: string;
  avg_processing_time_seconds: number;
  median_processing_time_seconds: number;
  min_processing_time_seconds: number;
  max_processing_time_seconds: number;
  total_processed_today: number;
  success_rate: number;
}

interface BottleneckAnalysis {
  stage: string;
  bottleneck_score: number;
  pending_count: number;
  avg_wait_time_hours: number;
  recommendation: string;
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const timePeriod = searchParams.get('period') || 'daily';
  const stage = searchParams.get('stage') || 'all';
  const specificDate = searchParams.get('date');
  const specificMonth = searchParams.get('month');
  const specificYear = searchParams.get('year');
  const enableRealTime = searchParams.get('realtime') === 'true';

  // Analytics configuration
  const config: AnalyticsConfig = {
    enableRealTimeMetrics: enableRealTime,
    enablePredictiveAnalytics: true,
    enableBottleneckDetection: true,
    cacheTimeout: 300 // 5 minutes
  };

  try {
    const client = await pool.connect();

    // IMPORTANT: This API uses the correct business logic for stage eligibility:
    // - Email Verification: Only contacts with email addresses
    // - OSINT Research: Only contacts with completed email verification
    // - Overview Extraction: Only contacts with completed OSINT research
    // - Classification: Only contacts with completed overview extraction
    // - Email Generation: Only contacts with completed classification
    // - Email Sending: Only contacts with completed email generation

        // Get processing volume trends
    const getTimeSeriesQuery = () => {
      let periods = timePeriod === 'daily' ? 7 : timePeriod === 'weekly' ? 4 : 12;
      const intervalUnit = timePeriod === 'daily' ? 'day' : timePeriod === 'weekly' ? 'week' : 'month';
      const truncUnit = timePeriod === 'daily' ? 'day' : timePeriod === 'weekly' ? 'week' : 'month';
      
      // Adjust date range based on specific date/month/year filters
      let dateFilter = '';
      if (specificDate) {
        dateFilter = `AND created_at::date = '${specificDate}'`;
        periods = 1; // Only show that specific day
      } else if (specificMonth) {
        dateFilter = `AND DATE_TRUNC('month', created_at) = '${specificMonth}-01'::date`;
        periods = 31; // Show all days in that month
      } else if (specificYear) {
        dateFilter = `AND DATE_TRUNC('year', created_at) = '${specificYear}-01-01'::date`;
        periods = 12; // Show all months in that year
      }

      return `
        WITH date_series AS (
          SELECT generate_series(
            CURRENT_DATE - INTERVAL '${periods - 1} ${intervalUnit}',
            CURRENT_DATE,
            INTERVAL '1 ${intervalUnit}'
          )::date AS date
        ),
        processing_data AS (
          SELECT 
            DATE_TRUNC('${truncUnit}', created_at) AS period_date,
            -- Count contacts that have completed any stage
            COUNT(CASE WHEN email_verification_status = 'completed' OR osint_status = 'completed' OR 
                              overview_extraction_status = 'completed' OR classification_status = 'completed' OR
                              email_generation_status = 'completed' OR email_sending_status = 'completed' 
                       THEN 1 END) as completed,
            -- Count contacts that have failed any stage
            COUNT(CASE WHEN email_verification_status = 'failed' OR osint_status = 'failed' OR 
                              overview_extraction_status = 'failed' OR classification_status = 'failed' OR
                              email_generation_status = 'failed' OR email_sending_status = 'failed' 
                       THEN 1 END) as failed,
            -- Count contacts that are pending (following correct stage eligibility)
            COUNT(CASE WHEN (email IS NOT NULL AND email != '' AND email != ' ' AND 
                            COALESCE(email_verification_status, 'pending') = 'pending') OR
                           (email_verification_status = 'completed' AND 
                            COALESCE(osint_status, 'pending') = 'pending') OR
                           (osint_status = 'completed' AND 
                            COALESCE(overview_extraction_status, 'pending') = 'pending') OR
                           (overview_extraction_status = 'completed' AND 
                            COALESCE(classification_status, 'pending') = 'pending') OR
                           (classification_status = 'completed' AND 
                            COALESCE(email_generation_status, 'pending') = 'pending') OR
                           (email_generation_status = 'completed' AND 
                            COALESCE(email_sending_status, 'pending') = 'pending')
                       THEN 1 END) as pending
          FROM contacts 
          WHERE created_at >= CURRENT_DATE - INTERVAL '${periods} ${intervalUnit}' ${dateFilter}
          GROUP BY period_date
        )
        SELECT 
          ds.date,
          COALESCE(pd.completed, 0) as completed,
          COALESCE(pd.failed, 0) as failed,
          COALESCE(pd.pending, 0) as pending
        FROM date_series ds
        LEFT JOIN processing_data pd ON ds.date = pd.period_date::date
        ORDER BY ds.date;
      `;
    };

    // Get stage performance data - using correct eligibility logic from stats API
    const stagePerformanceQuery = `
      SELECT 
        'email_verification' as stage,
        COUNT(CASE WHEN email_verification_status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN email_verification_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN email IS NOT NULL AND email != '' AND email != ' ' AND COALESCE(email_verification_status, 'pending') = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN email_verification_status = 'running' THEN 1 END) as running
      FROM contacts
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND email IS NOT NULL AND email != '' AND email != ' '
      
      UNION ALL
      
      SELECT 
        'osint' as stage,
        COUNT(CASE WHEN osint_status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN osint_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN email_verification_status = 'completed' AND COALESCE(osint_status, 'pending') = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN osint_status = 'running' THEN 1 END) as running
      FROM contacts
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND email_verification_status = 'completed'
      
      UNION ALL
      
      SELECT 
        'overview_extraction' as stage,
        COUNT(CASE WHEN overview_extraction_status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN overview_extraction_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN osint_status = 'completed' AND COALESCE(overview_extraction_status, 'pending') = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN overview_extraction_status = 'running' THEN 1 END) as running
      FROM contacts
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND osint_status = 'completed'
      
      UNION ALL
      
      SELECT 
        'classification' as stage,
        COUNT(CASE WHEN classification_status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN classification_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN overview_extraction_status = 'completed' AND COALESCE(classification_status, 'pending') = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN classification_status = 'running' THEN 1 END) as running
      FROM contacts
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND overview_extraction_status = 'completed'
      
      UNION ALL
      
      SELECT 
        'email_generation' as stage,
        COUNT(CASE WHEN email_generation_status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN email_generation_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN classification_status = 'completed' AND COALESCE(email_generation_status, 'pending') = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN email_generation_status = 'running' THEN 1 END) as running
      FROM contacts
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND classification_status = 'completed'
      
      UNION ALL
      
      SELECT 
        'email_sending' as stage,
        COUNT(CASE WHEN email_sending_status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN email_sending_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN email_generation_status = 'completed' AND COALESCE(email_sending_status, 'pending') = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN email_sending_status = 'running' THEN 1 END) as running
      FROM contacts
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND email_generation_status = 'completed'
      
      ORDER BY stage;
    `;

    // Get error analysis - using correct eligibility logic
    const errorAnalysisQuery = `
      WITH error_totals AS (
        SELECT 
          error_type,
          COUNT(*) as count
        FROM (
          SELECT 
            CASE 
              WHEN email_verification_error ILIKE '%invalid%' OR email_verification_error ILIKE '%not found%' THEN 'Invalid Email'
              WHEN osint_error ILIKE '%linkedin%' OR osint_error ILIKE '%not found%' THEN 'LinkedIn Not Found'
              WHEN email_verification_error ILIKE '%timeout%' OR osint_error ILIKE '%timeout%' OR 
                   overview_extraction_error ILIKE '%timeout%' OR classification_error ILIKE '%timeout%' OR
                   email_generation_error ILIKE '%timeout%' OR email_sending_error ILIKE '%timeout%' THEN 'API Timeout'
              WHEN email_verification_error ILIKE '%rate%' OR osint_error ILIKE '%rate%' OR 
                   overview_extraction_error ILIKE '%rate%' OR classification_error ILIKE '%rate%' OR
                   email_generation_error ILIKE '%rate%' OR email_sending_error ILIKE '%rate%' THEN 'Rate Limited'
              ELSE 'Other'
            END as error_type
          FROM contacts 
          WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
            AND (
              -- Email verification errors (eligible: contacts with emails)
              (email IS NOT NULL AND email != '' AND email != ' ' AND email_verification_status = 'failed') OR
              -- OSINT errors (eligible: contacts with verified emails)
              (email_verification_status = 'completed' AND osint_status = 'failed') OR
              -- Overview extraction errors (eligible: contacts with completed OSINT)
              (osint_status = 'completed' AND overview_extraction_status = 'failed') OR
              -- Classification errors (eligible: contacts with completed overview extraction)
              (overview_extraction_status = 'completed' AND classification_status = 'failed') OR
              -- Email generation errors (eligible: contacts with completed classification)
              (classification_status = 'completed' AND email_generation_status = 'failed') OR
              -- Email sending errors (eligible: contacts with completed email generation)
              (email_generation_status = 'completed' AND email_sending_status = 'failed')
            )
        ) error_summary
        GROUP BY error_type
      )
      SELECT 
        error_type,
        count,
        ROUND(count * 100.0 / SUM(count) OVER (), 1) as percentage
      FROM error_totals
      ORDER BY count DESC
      LIMIT 10;
    `;

    // Get real-time processing speed metrics
    const processingSpeedQuery = `
      WITH stage_processing_times AS (
        SELECT
          'email_verification' as stage,
          EXTRACT(EPOCH FROM (email_verification_date - created_at)) as processing_time_seconds,
          CASE WHEN email_verification_status = 'completed' THEN 1 ELSE 0 END as success
        FROM contacts
        WHERE email_verification_date IS NOT NULL
          AND created_at >= CURRENT_DATE - INTERVAL '7 days'
          AND email IS NOT NULL AND email != '' AND email != ' '

        UNION ALL

        SELECT
          'osint' as stage,
          EXTRACT(EPOCH FROM (osint_completion_date - email_verification_date)) as processing_time_seconds,
          CASE WHEN osint_status = 'completed' THEN 1 ELSE 0 END as success
        FROM contacts
        WHERE osint_completion_date IS NOT NULL
          AND email_verification_date IS NOT NULL
          AND created_at >= CURRENT_DATE - INTERVAL '7 days'

        UNION ALL

        SELECT
          'overview_extraction' as stage,
          EXTRACT(EPOCH FROM (overview_extraction_date - osint_completion_date)) as processing_time_seconds,
          CASE WHEN overview_extraction_status = 'completed' THEN 1 ELSE 0 END as success
        FROM contacts
        WHERE overview_extraction_date IS NOT NULL
          AND osint_completion_date IS NOT NULL
          AND created_at >= CURRENT_DATE - INTERVAL '7 days'

        UNION ALL

        SELECT
          'classification' as stage,
          EXTRACT(EPOCH FROM (classification_date - overview_extraction_date)) as processing_time_seconds,
          CASE WHEN classification_status = 'completed' THEN 1 ELSE 0 END as success
        FROM contacts
        WHERE classification_date IS NOT NULL
          AND overview_extraction_date IS NOT NULL
          AND created_at >= CURRENT_DATE - INTERVAL '7 days'

        UNION ALL

        SELECT
          'email_generation' as stage,
          EXTRACT(EPOCH FROM (email_generation_date - classification_date)) as processing_time_seconds,
          CASE WHEN email_generation_status = 'completed' THEN 1 ELSE 0 END as success
        FROM contacts
        WHERE email_generation_date IS NOT NULL
          AND classification_date IS NOT NULL
          AND created_at >= CURRENT_DATE - INTERVAL '7 days'

        UNION ALL

        SELECT
          'email_sending' as stage,
          EXTRACT(EPOCH FROM (email_sending_date - email_generation_date)) as processing_time_seconds,
          CASE WHEN email_sending_status = 'completed' THEN 1 ELSE 0 END as success
        FROM contacts
        WHERE email_sending_date IS NOT NULL
          AND email_generation_date IS NOT NULL
          AND created_at >= CURRENT_DATE - INTERVAL '7 days'
      )
      SELECT
        stage,
        ROUND(AVG(processing_time_seconds), 1) as avg_processing_time_seconds,
        ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY processing_time_seconds), 1) as median_processing_time_seconds,
        ROUND(MIN(processing_time_seconds), 1) as min_processing_time_seconds,
        ROUND(MAX(processing_time_seconds), 1) as max_processing_time_seconds,
        COUNT(*) as total_processed_today,
        ROUND(AVG(success) * 100, 1) as success_rate
      FROM stage_processing_times
      WHERE processing_time_seconds > 0 AND processing_time_seconds < 86400 -- Filter out invalid times
      GROUP BY stage
      ORDER BY
        CASE stage
          WHEN 'email_verification' THEN 1
          WHEN 'osint' THEN 2
          WHEN 'overview_extraction' THEN 3
          WHEN 'classification' THEN 4
          WHEN 'email_generation' THEN 5
          WHEN 'email_sending' THEN 6
        END;
    `;

    // Get enhanced throughput metrics with bottleneck detection
    const throughputQuery = `
      WITH hourly_processing AS (
        SELECT
          DATE_TRUNC('hour', created_at) as hour,
          COUNT(*) as contacts_created,
          COUNT(CASE WHEN email_verification_status = 'completed' THEN 1 END) as email_verified,
          COUNT(CASE WHEN osint_status = 'completed' THEN 1 END) as osint_completed,
          COUNT(CASE WHEN overview_extraction_status = 'completed' THEN 1 END) as overview_completed,
          COUNT(CASE WHEN classification_status = 'completed' THEN 1 END) as classified,
          COUNT(CASE WHEN email_generation_status = 'completed' THEN 1 END) as emails_generated,
          COUNT(CASE WHEN email_sending_status = 'completed' THEN 1 END) as emails_sent
        FROM contacts
        WHERE created_at >= CURRENT_DATE
        GROUP BY DATE_TRUNC('hour', created_at)
      )
      SELECT
        COALESCE(SUM(contacts_created), 0) as contacts_today,
        ROUND(COALESCE(AVG(contacts_created), 0), 0) as avg_per_hour,
        COALESCE(MAX(contacts_created), 0) as peak_hour,
        ROUND(COALESCE(AVG(contacts_created), 0), 0) as avg_hourly,
        COALESCE(SUM(email_verified), 0) as emails_verified_today,
        COALESCE(SUM(osint_completed), 0) as osint_completed_today,
        COALESCE(SUM(overview_completed), 0) as overview_completed_today,
        COALESCE(SUM(classified), 0) as classified_today,
        COALESCE(SUM(emails_generated), 0) as emails_generated_today,
        COALESCE(SUM(emails_sent), 0) as emails_sent_today
      FROM hourly_processing;
    `;

    // Get bottleneck analysis
    const bottleneckQuery = `
      WITH stage_metrics AS (
        SELECT
          'email_verification' as stage,
          COUNT(CASE WHEN email IS NOT NULL AND email != '' AND email != ' ' AND COALESCE(email_verification_status, 'pending') = 'pending' THEN 1 END) as pending_count,
          ROUND(AVG(EXTRACT(EPOCH FROM (NOW() - created_at)) / 3600), 1) as avg_wait_time_hours
        FROM contacts
        WHERE email IS NOT NULL AND email != '' AND email != ' '

        UNION ALL

        SELECT
          'osint' as stage,
          COUNT(CASE WHEN email_verification_status = 'completed' AND COALESCE(osint_status, 'pending') = 'pending' THEN 1 END) as pending_count,
          ROUND(AVG(EXTRACT(EPOCH FROM (NOW() - email_verification_date)) / 3600), 1) as avg_wait_time_hours
        FROM contacts
        WHERE email_verification_status = 'completed'

        UNION ALL

        SELECT
          'overview_extraction' as stage,
          COUNT(CASE WHEN osint_status = 'completed' AND COALESCE(overview_extraction_status, 'pending') = 'pending' THEN 1 END) as pending_count,
          ROUND(AVG(EXTRACT(EPOCH FROM (NOW() - osint_completion_date)) / 3600), 1) as avg_wait_time_hours
        FROM contacts
        WHERE osint_status = 'completed'

        UNION ALL

        SELECT
          'classification' as stage,
          COUNT(CASE WHEN overview_extraction_status = 'completed' AND COALESCE(classification_status, 'pending') = 'pending' THEN 1 END) as pending_count,
          ROUND(AVG(EXTRACT(EPOCH FROM (NOW() - overview_extraction_date)) / 3600), 1) as avg_wait_time_hours
        FROM contacts
        WHERE overview_extraction_status = 'completed'

        UNION ALL

        SELECT
          'email_generation' as stage,
          COUNT(CASE WHEN classification_status = 'completed' AND COALESCE(email_generation_status, 'pending') = 'pending' THEN 1 END) as pending_count,
          ROUND(AVG(EXTRACT(EPOCH FROM (NOW() - classification_date)) / 3600), 1) as avg_wait_time_hours
        FROM contacts
        WHERE classification_status = 'completed'

        UNION ALL

        SELECT
          'email_sending' as stage,
          COUNT(CASE WHEN email_generation_status = 'completed' AND COALESCE(email_sending_status, 'pending') = 'pending' THEN 1 END) as pending_count,
          ROUND(AVG(EXTRACT(EPOCH FROM (NOW() - email_generation_date)) / 3600), 1) as avg_wait_time_hours
        FROM contacts
        WHERE email_generation_status = 'completed'
      )
      SELECT
        stage,
        pending_count,
        COALESCE(avg_wait_time_hours, 0) as avg_wait_time_hours,
        CASE
          WHEN pending_count > 1000 AND avg_wait_time_hours > 24 THEN 90
          WHEN pending_count > 500 AND avg_wait_time_hours > 12 THEN 70
          WHEN pending_count > 100 AND avg_wait_time_hours > 6 THEN 50
          WHEN pending_count > 50 AND avg_wait_time_hours > 3 THEN 30
          ELSE 10
        END as bottleneck_score,
        CASE
          WHEN pending_count > 1000 AND avg_wait_time_hours > 24 THEN 'Critical bottleneck: Increase processing capacity immediately'
          WHEN pending_count > 500 AND avg_wait_time_hours > 12 THEN 'Major bottleneck: Scale up processing resources'
          WHEN pending_count > 100 AND avg_wait_time_hours > 6 THEN 'Moderate bottleneck: Monitor and consider scaling'
          WHEN pending_count > 50 AND avg_wait_time_hours > 3 THEN 'Minor bottleneck: Normal processing delays'
          ELSE 'No bottleneck detected: Processing normally'
        END as recommendation
      FROM stage_metrics
      ORDER BY bottleneck_score DESC;
    `;

    // Execute all queries with enhanced analytics
    const queryPromises = [
      client.query(getTimeSeriesQuery()),
      client.query(stagePerformanceQuery),
      client.query(errorAnalysisQuery),
      client.query(processingSpeedQuery),
      client.query(throughputQuery)
    ];

    // Add bottleneck analysis if enabled
    if (config.enableBottleneckDetection) {
      queryPromises.push(client.query(bottleneckQuery));
    }

    const results = await Promise.all(queryPromises);
    const [timeSeriesResult, stagePerformanceResult, errorAnalysisResult, processingSpeedResult, throughputResult, bottleneckResult] = results;

    client.release();

    // Process and enhance the data
    const enhancedData = {
      timeSeriesData: timeSeriesResult.rows,
      stagePerformance: stagePerformanceResult.rows,
      errorAnalysis: errorAnalysisResult.rows,
      processingSpeed: processingSpeedResult.rows.map((row: any) => ({
        ...row,
        avg_time_formatted: formatProcessingTime(row.avg_processing_time_seconds),
        median_time_formatted: formatProcessingTime(row.median_processing_time_seconds),
        efficiency_score: calculateEfficiencyScore(row)
      })),
      throughput: throughputResult.rows[0] || {
        contacts_today: 0,
        avg_per_hour: 0,
        peak_hour: 0,
        avg_hourly: 0,
        emails_verified_today: 0,
        osint_completed_today: 0,
        overview_completed_today: 0,
        classified_today: 0,
        emails_generated_today: 0,
        emails_sent_today: 0
      },
      bottleneckAnalysis: config.enableBottleneckDetection ? bottleneckResult?.rows || [] : [],
      metadata: {
        timePeriod,
        stage,
        lastUpdated: new Date().toISOString(),
        dataSource: 'contacts table',
        analyticsConfig: config,
        queryExecutionTime: Date.now(),
        realTimeEnabled: config.enableRealTimeMetrics
      }
    };

    return NextResponse.json({
      success: true,
      data: enhancedData
    });

  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch analytics data',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Helper functions for enhanced analytics
function formatProcessingTime(seconds: number): string {
  if (!seconds || seconds < 0) return 'N/A';

  if (seconds < 60) {
    return `${Math.round(seconds)}s`;
  } else if (seconds < 3600) {
    return `${Math.round(seconds / 60)}m ${Math.round(seconds % 60)}s`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  }
}

function calculateEfficiencyScore(speedMetric: any): number {
  const { success_rate, avg_processing_time_seconds, total_processed_today } = speedMetric;

  // Base score from success rate (0-40 points)
  let score = (success_rate || 0) * 0.4;

  // Processing speed score (0-30 points) - lower time is better
  const speedScore = Math.max(0, 30 - (avg_processing_time_seconds || 0) / 60);
  score += speedScore;

  // Volume score (0-30 points) - more processed is better
  const volumeScore = Math.min(30, (total_processed_today || 0) / 10);
  score += volumeScore;

  return Math.round(Math.min(100, Math.max(0, score)));
}
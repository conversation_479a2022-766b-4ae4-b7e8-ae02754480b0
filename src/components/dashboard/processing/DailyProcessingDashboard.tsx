"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardHeader, CardTitle, CardContent } from '../../ui/card'
import { Button } from '../../ui/button'
import { RefreshCw, Calendar, TrendingUp, BarChart3 } from 'lucide-react'
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  Area,
  AreaChart
} from 'recharts'

interface StageData {
  stage: string
  completed: number
  failed: number
  pending: number
  running: number
  total: number
  successRate: number
  name: string
  color: string
  order: number
}

interface TimeSeriesData {
  date: string
  completed: number
  failed: number
  pending: number
}

interface DashboardData {
  timeSeriesData: TimeSeriesData[]
  stagePerformance: StageData[]
  throughput: {
    contacts_today: number
    avg_per_hour: number
    peak_hour: number
    avg_hourly: number
  }
}

const DailyProcessingDashboard = () => {
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(false)
  const [timePeriod, setTimePeriod] = useState<'daily' | 'weekly' | 'monthly'>('daily')

  // Stage configuration with proper ordering and visual styling
  const stageConfig = [
    { key: 'email_verification', name: 'Email Verification', icon: '📧', color: '#3b82f6', order: 1 },
    { key: 'osint', name: 'OSINT Research', icon: '🔍', color: '#8b5cf6', order: 2 },
    { key: 'overview_extraction', name: 'Overview Extraction', icon: '📄', color: '#06b6d4', order: 3 },
    { key: 'classification', name: 'Classification', icon: '🏷️', color: '#10b981', order: 4 },
    { key: 'email_generation', name: 'Email Generation', icon: '✍️', color: '#f59e0b', order: 5 },
    { key: 'email_sending', name: 'Email Sending', icon: '📤', color: '#ef4444', order: 6 }
  ]

  useEffect(() => {
    fetchData()
  }, [timePeriod])

  const fetchData = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/processing/analytics?period=${timePeriod}`)
      const result = await response.json()
      
      if (result.success) {
        // Process stage performance data and sort by order
        const sortedStageData = result.data.stagePerformance
          .map((stage: any) => {
            const config = stageConfig.find(c => c.key === stage.stage)
            const completed = parseInt(stage.completed) || 0
            const failed = parseInt(stage.failed) || 0
            const pending = parseInt(stage.pending) || 0
            const running = parseInt(stage.running) || 0
            const total = completed + failed + pending + running
            const successRate = total > 0 ? (completed / total) * 100 : 0

            return {
              ...stage,
              completed,
              failed,
              pending,
              running,
              total,
              successRate,
              name: config?.name || stage.stage,
              color: config?.color || '#6b7280',
              order: config?.order || 999
            }
          })
          .sort((a: any, b: any) => a.order - b.order)

        setData({
          timeSeriesData: result.data.timeSeriesData || [],
          stagePerformance: sortedStageData,
          throughput: result.data.throughput || { contacts_today: 0, avg_per_hour: 0, peak_hour: 0, avg_hourly: 0 }
        })
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    if (timePeriod === 'daily') {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    } else if (timePeriod === 'weekly') {
      return `Week ${Math.ceil(date.getDate() / 7)}`
    } else {
      return date.toLocaleDateString('en-US', { month: 'short' })
    }
  }

  const getTotalsByStatus = () => {
    if (!data?.stagePerformance) return { completed: 0, failed: 0, pending: 0, running: 0 }
    
    return data.stagePerformance.reduce((acc, stage) => ({
      completed: acc.completed + stage.completed,
      failed: acc.failed + stage.failed,
      pending: acc.pending + stage.pending,
      running: acc.running + stage.running
    }), { completed: 0, failed: 0, pending: 0, running: 0 })
  }

  const totals = getTotalsByStatus()

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Daily Processing Dashboard
              {loading && <RefreshCw className="h-4 w-4 animate-spin" />}
            </CardTitle>
            
            <div className="flex items-center gap-4">
              {/* Time Period Selector */}
              <div className="flex gap-2">
                <Button
                  variant={timePeriod === 'daily' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimePeriod('daily')}
                >
                  Daily (7d)
                </Button>
                <Button
                  variant={timePeriod === 'weekly' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimePeriod('weekly')}
                >
                  Weekly (4w)
                </Button>
                <Button
                  variant={timePeriod === 'monthly' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimePeriod('monthly')}
                >
                  Monthly (12m)
                </Button>
              </div>

              <Button onClick={fetchData} size="sm" variant="outline">
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{totals.completed.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{totals.running.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Running</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{totals.pending.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Pending</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{totals.failed.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Failed</div>
          </CardContent>
        </Card>
      </div>

      {/* Processing Trend Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Processing Trends Over Time
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={data?.timeSeriesData || []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tickFormatter={formatDate}
                fontSize={12}
              />
              <YAxis fontSize={12} />
              <Tooltip 
                labelFormatter={(value) => formatDate(value as string)}
                formatter={(value: number, name: string) => [
                  value.toLocaleString(), 
                  name.charAt(0).toUpperCase() + name.slice(1)
                ]}
              />
              <Area 
                type="monotone" 
                dataKey="completed" 
                stackId="1" 
                stroke="#22c55e" 
                fill="#22c55e" 
                fillOpacity={0.8}
                name="completed" 
              />
              <Area 
                type="monotone" 
                dataKey="pending" 
                stackId="1" 
                stroke="#f59e0b" 
                fill="#f59e0b" 
                fillOpacity={0.8}
                name="pending" 
              />
              <Area 
                type="monotone" 
                dataKey="failed" 
                stackId="1" 
                stroke="#ef4444" 
                fill="#ef4444" 
                fillOpacity={0.8}
                name="failed" 
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Stage Processing Pipeline */}
      <Card>
        <CardHeader>
          <CardTitle>Processing Pipeline Status</CardTitle>
          <p className="text-sm text-muted-foreground">
            Current status of each processing stage showing the sequential workflow
          </p>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart 
              data={data?.stagePerformance || []} 
              layout="horizontal"
              margin={{ top: 20, right: 30, left: 100, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" fontSize={12} />
              <YAxis 
                type="category" 
                dataKey="name"
                width={120}
                fontSize={12}
              />
              <Tooltip 
                formatter={(value: number, name: string) => [
                  value.toLocaleString(), 
                  name.charAt(0).toUpperCase() + name.slice(1)
                ]}
              />
              <Bar dataKey="completed" stackId="a" fill="#22c55e" name="completed" />
              <Bar dataKey="running" stackId="a" fill="#3b82f6" name="running" />
              <Bar dataKey="pending" stackId="a" fill="#f59e0b" name="pending" />
              <Bar dataKey="failed" stackId="a" fill="#ef4444" name="failed" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Stage Details Table */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Stage Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-3">Stage</th>
                  <th className="text-center p-3">Completed</th>
                  <th className="text-center p-3">Running</th>
                  <th className="text-center p-3">Pending</th>
                  <th className="text-center p-3">Failed</th>
                  <th className="text-center p-3">Total</th>
                  <th className="text-center p-3">Success Rate</th>
                </tr>
              </thead>
              <tbody>
                {data?.stagePerformance.map((stage, index) => {
                  const config = stageConfig.find(c => c.key === stage.stage)
                  return (
                    <tr key={stage.stage} className="border-b hover:bg-gray-50">
                      <td className="p-3">
                        <div className="flex items-center gap-3">
                          <div className="text-2xl">{config?.icon}</div>
                          <div>
                            <div className="font-medium">{stage.name}</div>
                            <div className="text-xs text-gray-500">Stage {index + 1}</div>
                          </div>
                        </div>
                      </td>
                      <td className="text-center p-3">
                        <span className="inline-block px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                          {stage.completed.toLocaleString()}
                        </span>
                      </td>
                      <td className="text-center p-3">
                        <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                          {stage.running.toLocaleString()}
                        </span>
                      </td>
                      <td className="text-center p-3">
                        <span className="inline-block px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
                          {stage.pending.toLocaleString()}
                        </span>
                      </td>
                      <td className="text-center p-3">
                        <span className="inline-block px-2 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                          {stage.failed.toLocaleString()}
                        </span>
                      </td>
                      <td className="text-center p-3 font-medium">{stage.total.toLocaleString()}</td>
                      <td className="text-center p-3">
                        <span className={`font-medium ${
                          stage.successRate >= 80 ? 'text-green-600' : 
                          stage.successRate >= 60 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {stage.successRate.toFixed(1)}%
                        </span>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Throughput Metrics */}
      {data?.throughput && (
        <Card>
          <CardHeader>
            <CardTitle>Today's Throughput</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {data.throughput.contacts_today.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Contacts Today</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {data.throughput.avg_per_hour.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Avg/Hour</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {data.throughput.peak_hour.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Peak Hour</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {data.throughput.avg_hourly.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Avg Hourly</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default DailyProcessingDashboard 